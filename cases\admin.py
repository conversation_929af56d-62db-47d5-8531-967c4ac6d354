from django.contrib import admin
from .models import Court, CaseType, Party, Case, Judgment

@admin.register(Court)
class CourtAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'phone']
    list_filter = ['type']
    search_fields = ['name', 'type']

@admin.register(CaseType)
class CaseTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']

@admin.register(Party)
class PartyAdmin(admin.ModelAdmin):
    list_display = ['name', 'party_type', 'national_id', 'phone']
    list_filter = ['party_type']
    search_fields = ['name', 'national_id', 'phone']

@admin.register(Case)
class CaseAdmin(admin.ModelAdmin):
    list_display = ['case_number', 'title', 'case_type', 'court', 'status', 'registration_date']
    list_filter = ['status', 'case_type', 'court', 'registration_date']
    search_fields = ['case_number', 'title', 'description']
    filter_horizontal = ['parties']
    date_hierarchy = 'registration_date'

@admin.register(Judgment)
class JudgmentAdmin(admin.ModelAdmin):
    list_display = ['judgment_number', 'case', 'judgment_type', 'judgment_date', 'is_final']
    list_filter = ['judgment_type', 'is_final', 'judgment_date']
    search_fields = ['judgment_number', 'case__case_number', 'judge_name']
    date_hierarchy = 'judgment_date'
