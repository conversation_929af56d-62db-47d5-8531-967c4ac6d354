from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.utils import timezone
from .models import Notification

@login_required
def notification_list(request):
    """قائمة التنبيهات"""
    notifications = Notification.objects.filter(
        recipient=request.user
    ).order_by('-created_at')

    # التصفح
    paginator = Paginator(notifications, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'notifications/notification_list.html', {'page_obj': page_obj})

@login_required
def notification_read(request, pk):
    """تحديد التنبيه كمقروء"""
    notification = get_object_or_404(Notification, pk=pk, recipient=request.user)

    if not notification.is_read:
        notification.is_read = True
        notification.read_at = timezone.now()
        notification.save()

    return redirect('notifications:list')

@login_required
def mark_all_read(request):
    """تحديد جميع التنبيهات كمقروءة"""
    Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).update(
        is_read=True,
        read_at=timezone.now()
    )

    messages.success(request, 'تم تحديد جميع التنبيهات كمقروءة')
    return redirect('notifications:list')
