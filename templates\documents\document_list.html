{% extends 'base.html' %}
{% load static %}

{% block title %}الأرشيف الإلكتروني - نظام إدارة دعاوى المحاكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open"></i>
        الأرشيف الإلكتروني
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'documents:add' %}" class="btn btn-primary">
                <i class="fas fa-upload"></i>
                رفع مستند جديد
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>عنوان المستند</th>
                        <th>نوع المستند</th>
                        <th>القضية</th>
                        <th>رفع بواسطة</th>
                        <th>تاريخ الرفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in page_obj %}
                    <tr>
                        <td>
                            <i class="fas fa-file-pdf text-danger me-2"></i>
                            <strong>{{ document.title }}</strong>
                            {% if document.is_confidential %}
                                <i class="fas fa-lock text-warning ms-2" title="مستند سري"></i>
                            {% endif %}
                        </td>
                        <td>{{ document.document_type.name }}</td>
                        <td>
                            <a href="{% url 'cases:detail' document.case.pk %}" class="text-decoration-none">
                                {{ document.case.case_number }}
                            </a>
                        </td>
                        <td>{{ document.uploaded_by.get_full_name|default:document.uploaded_by.username }}</td>
                        <td>{{ document.uploaded_at|date:"Y/m/d H:i" }}</td>
                        <td>
                            {% if document.is_confidential %}
                                <span class="badge bg-warning">سري</span>
                            {% else %}
                                <span class="badge bg-success">عام</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'documents:detail' document.pk %}" class="btn btn-outline-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'documents:download' document.pk %}" class="btn btn-outline-success" title="تحميل">
                                    <i class="fas fa-download"></i>
                                </a>
                                {% if user.is_staff %}
                                <a href="{% url 'documents:edit' document.pk %}" class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="تصفح المستندات">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-folder-open fa-3x mb-3"></i>
            <h4>لا توجد مستندات</h4>
            <p>لم يتم رفع أي مستندات بعد</p>
            <a href="{% url 'documents:add' %}" class="btn btn-primary">
                <i class="fas fa-upload"></i>
                رفع مستند جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
