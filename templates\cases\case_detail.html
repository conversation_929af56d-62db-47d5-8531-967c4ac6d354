{% extends 'base.html' %}
{% load static %}

{% block title %}{{ case.title }} - نظام إدارة دعاوى المحاكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-gavel"></i>
        تفاصيل القضية: {{ case.case_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'cases:edit' case.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i>
                تعديل
            </a>
            <a href="{% url 'cases:list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات القضية الأساسية -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-info-circle"></i>
                معلومات القضية
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم القضية:</strong></td>
                                <td>{{ case.case_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع القضية:</strong></td>
                                <td>{{ case.case_type.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>المحكمة:</strong></td>
                                <td>{{ case.court.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الدائرة:</strong></td>
                                <td>{{ case.circuit }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td><span class="badge status-{{ case.status }}">{{ case.get_status_display }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ القيد:</strong></td>
                                <td>{{ case.registration_date|date:"Y/m/d" }}</td>
                            </tr>
                            <tr>
                                <td><strong>المحامي المكلف:</strong></td>
                                <td>{{ case.assigned_lawyer.get_full_name|default:"غير محدد" }}</td>
                            </tr>
                            <tr>
                                <td><strong>أنشأ بواسطة:</strong></td>
                                <td>{{ case.created_by.get_full_name|default:case.created_by.username }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <hr>
                
                <h6><strong>عنوان القضية:</strong></h6>
                <p>{{ case.title }}</p>
                
                <h6><strong>وصف القضية:</strong></h6>
                <p class="text-muted">{{ case.description|linebreaks }}</p>
            </div>
        </div>

        <!-- الأطراف -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-users"></i>
                أطراف القضية
            </div>
            <div class="card-body">
                {% if case.parties.all %}
                    <div class="row">
                        {% for party in case.parties.all %}
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <h6>{{ party.name }}</h6>
                                <p class="mb-1"><strong>النوع:</strong> {{ party.get_party_type_display }}</p>
                                {% if party.national_id %}
                                    <p class="mb-1"><strong>رقم الهوية:</strong> {{ party.national_id }}</p>
                                {% endif %}
                                {% if party.phone %}
                                    <p class="mb-1"><strong>الهاتف:</strong> {{ party.phone }}</p>
                                {% endif %}
                                {% if party.email %}
                                    <p class="mb-0"><strong>البريد:</strong> {{ party.email }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <p>لم يتم إضافة أطراف لهذه القضية بعد</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- الإحصائيات السريعة -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i>
                إحصائيات سريعة
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ sessions.count }}</h4>
                        <small>الجلسات</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ documents.count }}</h4>
                        <small>المستندات</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-warning">{{ judgments.count }}</h4>
                        <small>الأحكام</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ case.parties.count }}</h4>
                        <small>الأطراف</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجلسات القادمة -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-calendar-alt"></i>
                الجلسات القادمة
            </div>
            <div class="card-body">
                {% if sessions %}
                    {% for session in sessions|slice:":3" %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small class="text-muted">جلسة {{ session.session_number }}</small>
                            <br>
                            <small>{{ session.session_date|date:"Y/m/d H:i" }}</small>
                        </div>
                        <span class="badge bg-info">{{ session.get_status_display }}</span>
                    </div>
                    {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                    
                    {% if sessions.count > 3 %}
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-sm btn-outline-primary">عرض جميع الجلسات</a>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-calendar-times fa-2x mb-2"></i>
                        <p class="small">لا توجد جلسات مجدولة</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- المستندات الحديثة -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-folder-open"></i>
                المستندات الحديثة
            </div>
            <div class="card-body">
                {% if documents %}
                    {% for document in documents|slice:":3" %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small class="fw-bold">{{ document.title|truncatechars:25 }}</small>
                            <br>
                            <small class="text-muted">{{ document.uploaded_at|date:"Y/m/d" }}</small>
                        </div>
                        <a href="{% url 'documents:download' document.pk %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download"></i>
                        </a>
                    </div>
                    {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                    
                    {% if documents.count > 3 %}
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-sm btn-outline-primary">عرض جميع المستندات</a>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-folder-open fa-2x mb-2"></i>
                        <p class="small">لا توجد مستندات</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
