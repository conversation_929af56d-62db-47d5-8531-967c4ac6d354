from django import forms
from .models import Case, CaseType, Court, Party

class CaseForm(forms.ModelForm):
    class Meta:
        model = Case
        fields = [
            'case_number', 'case_type', 'court', 'circuit', 
            'title', 'description', 'status', 'parties', 'assigned_lawyer'
        ]
        widgets = {
            'case_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم القضية'
            }),
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان القضية'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف القضية'
            }),
            'circuit': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الدائرة'
            }),
            'case_type': forms.Select(attrs={'class': 'form-select'}),
            'court': forms.Select(attrs={'class': 'form-select'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'assigned_lawyer': forms.Select(attrs={'class': 'form-select'}),
            'parties': forms.SelectMultiple(attrs={
                'class': 'form-select',
                'size': '5'
            }),
        }
        labels = {
            'case_number': 'رقم القضية',
            'case_type': 'نوع القضية',
            'court': 'المحكمة',
            'circuit': 'الدائرة',
            'title': 'عنوان القضية',
            'description': 'وصف القضية',
            'status': 'حالة القضية',
            'parties': 'الأطراف',
            'assigned_lawyer': 'المحامي المكلف',
        }

class PartyForm(forms.ModelForm):
    class Meta:
        model = Party
        fields = ['name', 'party_type', 'national_id', 'phone', 'email', 'address']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الكامل'
            }),
            'party_type': forms.Select(attrs={'class': 'form-select'}),
            'national_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهوية'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان'
            }),
        }
        labels = {
            'name': 'الاسم',
            'party_type': 'نوع الطرف',
            'national_id': 'رقم الهوية',
            'phone': 'الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
        }
