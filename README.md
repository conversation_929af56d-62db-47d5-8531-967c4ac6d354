# نظام إدارة دعاوى المحاكم

نظام شامل لإدارة القضايا والدعاوى في المحاكم، مطور باستخدام Django وBootstrap مع دعم كامل للغة العربية.

## 🌟 المميزات الرئيسية

### ✅ إدارة القضايا والدعاوى
- تسجيل دعوى جديدة (رقم القضية، تاريخ القيد، نوع الدعوى، المحكمة المختصة، الدائرة)
- تصنيف القضايا حسب النوع (مدنية، جنائية، إدارية... إلخ)
- بيانات الخصوم (المدعي، المدعى عليه، المحامي، الوكلاء القانونيين)
- متابعة حالة القضية (جديدة، جارية، مؤجلة، مغلقة...)
- ربط الوثائق والمستندات الخاصة بكل دعوى

### ✅ جلسات المحاكمة
- تحديد مواعيد الجلسات
- سجل محاضر الجلسات وتفاصيلها
- متابعة التأجيلات وسببها
- الربط مع تقويم زمني للتنبيهات

### ✅ القرارات والأحكام
- تسجيل الأحكام الصادرة وربطها بالقضية
- تصنيف الأحكام (أولية، نهائية، استئناف)
- حفظ نسخ من الأحكام بصيغ PDF أو Word
- الربط مع التمييز أو الطعن إن وجد

### ✅ أرشفة إلكترونية
- تخزين كافة المستندات الداعمة للقضية
- تصنيف المستندات حسب النوع
- صلاحيات الوصول حسب المستخدم
- تحميل وعرض المستندات

### ✅ التنبيهات والإشعارات
- تنبيهات أوتوماتيكية للجلسات القادمة
- تنبيه بانتهاء المهلة القانونية للاستئناف أو الطعن
- تنبيه بالقرارات الجديدة أو المستندات المضافة
- نظام أولويات للتنبيهات

### ✅ إدارة المستخدمين والصلاحيات
- أنواع مستخدمين متعددة (محامٍ، مساعد قانوني، كاتب، إداري، قاضٍ)
- تحديد صلاحيات لكل مستخدم
- سجل النشاط والتتبع

### ✅ التقارير والإحصائيات
- تقارير عدد القضايا حسب النوع أو المحكمة
- تقارير القضايا المنجزة والمتعثرة
- رسوم بيانية للإحصائيات
- لوحة تحكم تفاعلية

## 🛠️ التقنيات المستخدمة

- **Backend**: Django 4.2.7
- **Frontend**: Bootstrap 5.3 (RTL)
- **Database**: SQLite (قابل للتغيير إلى PostgreSQL/MySQL)
- **Icons**: Font Awesome 6.0
- **Fonts**: Cairo (Google Fonts)
- **Language**: Python 3.13

## 📋 متطلبات النظام

```bash
Python 3.8+
Django 4.2+
Pillow (للصور)
ReportLab (للتقارير)
Arabic-Reshaper (للنصوص العربية)
Python-BIDI (لدعم الاتجاه)
```

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd court_system
```

### 2. تثبيت المتطلبات
```bash
pip install django psycopg2-binary pillow reportlab arabic-reshaper python-bidi
```

### 3. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. إنشاء البيانات الأولية
```bash
python create_initial_data.py
```

### 5. تشغيل الخادم
```bash
python manage.py runserver
```

### 6. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://127.0.0.1:8000`

## 🔐 بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
court_system/
├── cases/              # إدارة القضايا
├── court_sessions/     # إدارة الجلسات
├── documents/          # الأرشيف الإلكتروني
├── notifications/      # التنبيهات
├── static/            # الملفات الثابتة
│   ├── css/           # ملفات الأنماط
│   ├── js/            # ملفات JavaScript
│   └── images/        # الصور والشعارات
├── templates/         # قوالب HTML
└── court_system/      # إعدادات المشروع
```

## 🎨 التصميم والواجهة

- **الخط**: Cairo Bold 12pt
- **الاتجاه**: من اليمين إلى اليسار (RTL)
- **الألوان**: تدرجات زرقاء وبنفسجية
- **التصميم**: متجاوب ومتوافق مع الأجهزة المحمولة
- **الشعار**: مصمم خصيصاً للنظام

## 📊 البيانات النموذجية

يتضمن النظام بيانات نموذجية تشمل:
- 5 محاكم مختلفة
- 7 أنواع قضايا
- 7 أنواع مستندات
- 4 أطراف نموذجية
- قضية نموذجية واحدة

## 🔧 التخصيص والتطوير

### إضافة محكمة جديدة
```python
Court.objects.create(
    name="اسم المحكمة",
    type="نوع المحكمة",
    address="العنوان"
)
```

### إضافة نوع قضية جديد
```python
CaseType.objects.create(
    name="نوع القضية",
    description="الوصف"
)
```

## 🛡️ الأمان

- تشفير كلمات المرور
- صلاحيات وصول متدرجة
- حماية من CSRF
- تسجيل العمليات والأنشطة

## 📱 التوافق

- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب للهواتف والأجهزة اللوحية
- دعم كامل للغة العربية
- سرعة تحميل عالية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +964-XXX-XXXX

---

**تم التطوير بـ ❤️ لخدمة العدالة والقانون**
