{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة دعاوى المحاكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-gavel"></i>
        {{ title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'cases:list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-edit"></i>
                بيانات القضية
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.case_number.id_for_label }}" class="form-label">{{ form.case_number.label }}</label>
                            {{ form.case_number }}
                            {% if form.case_number.errors %}
                                <div class="text-danger small">{{ form.case_number.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                            {{ form.status }}
                            {% if form.status.errors %}
                                <div class="text-danger small">{{ form.status.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.case_type.id_for_label }}" class="form-label">{{ form.case_type.label }}</label>
                            {{ form.case_type }}
                            {% if form.case_type.errors %}
                                <div class="text-danger small">{{ form.case_type.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.court.id_for_label }}" class="form-label">{{ form.court.label }}</label>
                            {{ form.court }}
                            {% if form.court.errors %}
                                <div class="text-danger small">{{ form.court.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.circuit.id_for_label }}" class="form-label">{{ form.circuit.label }}</label>
                        {{ form.circuit }}
                        {% if form.circuit.errors %}
                            <div class="text-danger small">{{ form.circuit.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }}</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger small">{{ form.title.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.assigned_lawyer.id_for_label }}" class="form-label">{{ form.assigned_lawyer.label }}</label>
                            {{ form.assigned_lawyer }}
                            {% if form.assigned_lawyer.errors %}
                                <div class="text-danger small">{{ form.assigned_lawyer.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.parties.id_for_label }}" class="form-label">{{ form.parties.label }}</label>
                            {{ form.parties }}
                            {% if form.parties.errors %}
                                <div class="text-danger small">{{ form.parties.errors }}</div>
                            {% endif %}
                            <div class="form-text">اضغط Ctrl لاختيار أكثر من طرف</div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'cases:list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i>
                معلومات مساعدة
            </div>
            <div class="card-body">
                <h6>إرشادات الإدخال:</h6>
                <ul class="small">
                    <li>رقم القضية يجب أن يكون فريداً</li>
                    <li>اختر نوع القضية والمحكمة المناسبة</li>
                    <li>أدخل عنواناً واضحاً ووصفاً مفصلاً</li>
                    <li>يمكن إضافة الأطراف لاحقاً</li>
                </ul>

                <hr>

                <h6>حالات القضية:</h6>
                <ul class="small">
                    <li><span class="badge status-new">جديدة</span> - قضية مسجلة حديثاً</li>
                    <li><span class="badge status-ongoing">جارية</span> - قضية قيد النظر</li>
                    <li><span class="badge status-postponed">مؤجلة</span> - قضية مؤجلة</li>
                    <li><span class="badge status-closed">مغلقة</span> - قضية منتهية</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
