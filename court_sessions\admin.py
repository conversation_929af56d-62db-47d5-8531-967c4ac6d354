from django.contrib import admin
from .models import Session, SessionDocument

@admin.register(Session)
class SessionAdmin(admin.ModelAdmin):
    list_display = ['case', 'session_number', 'session_date', 'status', 'judge_name']
    list_filter = ['status', 'session_date', 'case__court']
    search_fields = ['case__case_number', 'judge_name', 'courtroom']
    filter_horizontal = ['attendees']
    date_hierarchy = 'session_date'

@admin.register(SessionDocument)
class SessionDocumentAdmin(admin.ModelAdmin):
    list_display = ['title', 'session', 'document_type', 'uploaded_by', 'uploaded_at']
    list_filter = ['document_type', 'uploaded_at']
    search_fields = ['title', 'session__case__case_number']
