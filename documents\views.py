from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import HttpResponse
from .models import Document

@login_required
def document_list(request):
    """قائمة المستندات"""
    documents = Document.objects.all().order_by('-uploaded_at')

    # التصفح
    paginator = Paginator(documents, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'documents/document_list.html', {'page_obj': page_obj})

@login_required
def document_detail(request, pk):
    """تفاصيل المستند"""
    document = get_object_or_404(Document, pk=pk)
    return render(request, 'documents/document_detail.html', {'document': document})

@login_required
def document_add(request):
    """إضافة مستند جديد"""
    messages.info(request, 'هذه الميزة قيد التطوير')
    return redirect('documents:list')

@login_required
def document_edit(request, pk):
    """تعديل المستند"""
    messages.info(request, 'هذه الميزة قيد التطوير')
    return redirect('documents:list')

@login_required
def document_delete(request, pk):
    """حذف المستند"""
    messages.info(request, 'هذه الميزة قيد التطوير')
    return redirect('documents:list')

@login_required
def document_download(request, pk):
    """تحميل المستند"""
    document = get_object_or_404(Document, pk=pk)

    # فحص الصلاحيات
    if document.is_confidential and not request.user.is_staff:
        messages.error(request, 'ليس لديك صلاحية لتحميل هذا المستند')
        return redirect('documents:list')

    # إرجاع الملف
    response = HttpResponse(document.file.read(), content_type='application/octet-stream')
    response['Content-Disposition'] = f'attachment; filename="{document.file.name}"'
    return response
