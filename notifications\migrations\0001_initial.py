# Generated by Django 4.2.7 on 2025-06-06 10:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("cases", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("court_sessions", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="NotificationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email_notifications",
                    models.BooleanField(
                        default=True, verbose_name="تنبيهات البريد الإلكتروني"
                    ),
                ),
                (
                    "sms_notifications",
                    models.BooleanField(
                        default=False, verbose_name="تنبيهات الرسائل النصية"
                    ),
                ),
                (
                    "browser_notifications",
                    models.BooleanField(default=True, verbose_name="تنبيهات المتصفح"),
                ),
                (
                    "session_reminders",
                    models.BooleanField(default=True, verbose_name="تذكير الجلسات"),
                ),
                (
                    "deadline_reminders",
                    models.BooleanField(
                        default=True, verbose_name="تذكير المواعيد النهائية"
                    ),
                ),
                (
                    "case_updates",
                    models.BooleanField(default=True, verbose_name="تحديثات القضايا"),
                ),
                (
                    "reminder_hours_before",
                    models.PositiveIntegerField(
                        default=24, verbose_name="ساعات التذكير المسبق"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_settings",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "إعدادات التنبيه",
                "verbose_name_plural": "إعدادات التنبيهات",
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="العنوان")),
                ("message", models.TextField(verbose_name="الرسالة")),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("session_reminder", "تذكير بجلسة"),
                            ("deadline_reminder", "تذكير بموعد نهائي"),
                            ("case_update", "تحديث قضية"),
                            ("document_added", "إضافة مستند"),
                            ("judgment_issued", "صدور حكم"),
                        ],
                        max_length=20,
                        verbose_name="نوع التنبيه",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                ("is_read", models.BooleanField(default=False, verbose_name="مقروء")),
                ("is_sent", models.BooleanField(default=False, verbose_name="مرسل")),
                (
                    "scheduled_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="وقت الإرسال المجدول"
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="وقت الإرسال"
                    ),
                ),
                (
                    "read_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="وقت القراءة"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "case",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cases.case",
                        verbose_name="القضية المرتبطة",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستلم",
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="court_sessions.session",
                        verbose_name="الجلسة المرتبطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "تنبيه",
                "verbose_name_plural": "التنبيهات",
                "ordering": ["-created_at"],
            },
        ),
    ]
