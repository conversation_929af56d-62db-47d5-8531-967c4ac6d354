from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from .models import Session

@login_required
def session_list(request):
    """قائمة الجلسات"""
    sessions = Session.objects.all().order_by('-session_date')

    # التصفح
    paginator = Paginator(sessions, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'sessions/session_list.html', {'page_obj': page_obj})

@login_required
def session_detail(request, pk):
    """تفاصيل الجلسة"""
    session = get_object_or_404(Session, pk=pk)
    return render(request, 'sessions/session_detail.html', {'session': session})

@login_required
def session_add(request):
    """إضافة جلسة جديدة"""
    # سيتم تطوير هذا لاحقاً
    messages.info(request, 'هذه الميزة قيد التطوير')
    return redirect('sessions:list')

@login_required
def session_edit(request, pk):
    """تعديل الجلسة"""
    # سيتم تطوير هذا لاحقاً
    messages.info(request, 'هذه الميزة قيد التطوير')
    return redirect('sessions:list')

@login_required
def session_delete(request, pk):
    """حذف الجلسة"""
    # سيتم تطوير هذا لاحقاً
    messages.info(request, 'هذه الميزة قيد التطوير')
    return redirect('sessions:list')
