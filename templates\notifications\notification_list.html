{% extends 'base.html' %}
{% load static %}

{% block title %}التنبيهات - نظام إدارة دعاوى المحاكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-bell"></i>
        التنبيهات والإشعارات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'notifications:mark_all_read' %}" class="btn btn-outline-primary">
                <i class="fas fa-check-double"></i>
                تحديد الكل كمقروء
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if page_obj %}
        <div class="list-group list-group-flush">
            {% for notification in page_obj %}
            <div class="list-group-item {% if not notification.is_read %}list-group-item-light border-start border-primary border-4{% endif %}">
                <div class="d-flex w-100 justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-2">{{ notification.title }}</h6>
                            
                            <!-- أيقونة نوع التنبيه -->
                            {% if notification.notification_type == 'session_reminder' %}
                                <i class="fas fa-calendar-alt text-primary"></i>
                            {% elif notification.notification_type == 'deadline_reminder' %}
                                <i class="fas fa-clock text-warning"></i>
                            {% elif notification.notification_type == 'case_update' %}
                                <i class="fas fa-gavel text-info"></i>
                            {% elif notification.notification_type == 'document_added' %}
                                <i class="fas fa-file-plus text-success"></i>
                            {% elif notification.notification_type == 'judgment_issued' %}
                                <i class="fas fa-balance-scale text-danger"></i>
                            {% endif %}
                            
                            <!-- شارة الأولوية -->
                            <span class="badge ms-2 
                                {% if notification.priority == 'urgent' %}bg-danger
                                {% elif notification.priority == 'high' %}bg-warning
                                {% elif notification.priority == 'medium' %}bg-info
                                {% else %}bg-secondary{% endif %}">
                                {{ notification.get_priority_display }}
                            </span>
                            
                            {% if not notification.is_read %}
                                <span class="badge bg-primary ms-2">جديد</span>
                            {% endif %}
                        </div>
                        
                        <p class="mb-2">{{ notification.message }}</p>
                        
                        <div class="d-flex align-items-center text-muted small">
                            <i class="fas fa-clock me-1"></i>
                            <span>{{ notification.created_at|date:"Y/m/d H:i" }}</span>
                            
                            {% if notification.case %}
                                <span class="mx-2">•</span>
                                <i class="fas fa-gavel me-1"></i>
                                <a href="{% url 'cases:detail' notification.case.pk %}" class="text-decoration-none">
                                    {{ notification.case.case_number }}
                                </a>
                            {% endif %}
                            
                            {% if notification.session %}
                                <span class="mx-2">•</span>
                                <i class="fas fa-calendar-alt me-1"></i>
                                <span>جلسة {{ notification.session.session_number }}</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="ms-3">
                        {% if not notification.is_read %}
                            <a href="{% url 'notifications:read' notification.pk %}" class="btn btn-sm btn-outline-primary" title="تحديد كمقروء">
                                <i class="fas fa-check"></i>
                            </a>
                        {% else %}
                            <i class="fas fa-check-circle text-success" title="مقروء"></i>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- التصفح -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="تصفح التنبيهات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-bell-slash fa-3x mb-3"></i>
            <h4>لا توجد تنبيهات</h4>
            <p>لا توجد تنبيهات في الوقت الحالي</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- إحصائيات التنبيهات -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ page_obj.paginator.count }}</h5>
                <p class="card-text">إجمالي التنبيهات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">
                    {{ page_obj.object_list|length }}
                </h5>
                <p class="card-text">في هذه الصفحة</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
