from django.db import models
from django.contrib.auth.models import User
from cases.models import Case

class Session(models.Model):
    """جلسات المحاكمة"""
    STATUS_CHOICES = [
        ('scheduled', 'مجدولة'),
        ('held', 'تمت'),
        ('postponed', 'مؤجلة'),
        ('cancelled', 'ملغية'),
    ]

    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='sessions', verbose_name="القضية")
    session_number = models.PositiveIntegerField(verbose_name="رقم الجلسة")
    session_date = models.DateTimeField(verbose_name="تاريخ ووقت الجلسة")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled', verbose_name="حالة الجلسة")

    judge_name = models.CharField(max_length=200, verbose_name="اسم القاضي")
    courtroom = models.CharField(max_length=100, verbose_name="قاعة المحكمة")

    agenda = models.TextField(verbose_name="جدول أعمال الجلسة")
    minutes = models.TextField(blank=True, verbose_name="محضر الجلسة")

    postponement_reason = models.TextField(blank=True, verbose_name="سبب التأجيل")
    next_session_date = models.DateTimeField(null=True, blank=True, verbose_name="موعد الجلسة القادمة")

    attendees = models.ManyToManyField('cases.Party', blank=True, verbose_name="الحاضرون")

    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشأ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "جلسة"
        verbose_name_plural = "الجلسات"
        ordering = ['-session_date']
        unique_together = ['case', 'session_number']

    def __str__(self):
        return f"جلسة رقم {self.session_number} - {self.case.case_number}"

class SessionDocument(models.Model):
    """مستندات الجلسة"""
    session = models.ForeignKey(Session, on_delete=models.CASCADE, related_name='documents', verbose_name="الجلسة")
    title = models.CharField(max_length=200, verbose_name="عنوان المستند")
    document_type = models.CharField(max_length=100, verbose_name="نوع المستند")
    file = models.FileField(upload_to='session_documents/', verbose_name="الملف")

    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="رفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند جلسة"
        verbose_name_plural = "مستندات الجلسات"

    def __str__(self):
        return f"{self.title} - {self.session}"
