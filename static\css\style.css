/* نظام إدارة دعاوى المحاكم - الأنماط الأساسية */

/* استيراد خط Cairo */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* الإعدادات الأساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    font-size: 12pt;
    font-weight: 700;
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* الحاوي الرئيسي */
.container-fluid {
    padding: 0;
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    display: flex;
    align-items: center;
}

.navbar-brand img {
    margin-left: 10px;
    width: 40px;
    height: 40px;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 600;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background-color: rgba(255,255,255,0.1);
    border-radius: 5px;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    min-height: calc(100vh - 76px);
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 5px 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    margin-left: 10px;
    width: 20px;
}

/* المحتوى الرئيسي */
.main-content {
    padding: 30px;
    background-color: #f8f9fa;
    min-height: calc(100vh - 76px);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    padding: 15px 20px;
    font-weight: 700;
}

.card-body {
    padding: 25px;
}

/* الأزرار */
.btn {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    border-radius: 8px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

/* الجداول */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 700;
    border: none;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* النماذج */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* الشارات */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    margin-bottom: 20px;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stats-card p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    font-weight: 600;
}

/* الشعار */
.logo {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 76px;
        right: -250px;
        width: 250px;
        height: calc(100vh - 76px);
        transition: right 0.3s ease;
        z-index: 1000;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .card-body {
        padding: 15px;
    }
}

/* تحسينات إضافية للنصوص العربية */
.arabic-text {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    line-height: 1.8;
}

/* أنماط خاصة بحالات القضايا */
.status-new { background-color: #17a2b8; }
.status-ongoing { background-color: #ffc107; }
.status-postponed { background-color: #fd7e14; }
.status-closed { background-color: #28a745; }
.status-appealed { background-color: #dc3545; }

/* أنماط أولوية التنبيهات */
.priority-low { border-right: 4px solid #28a745; }
.priority-medium { border-right: 4px solid #ffc107; }
.priority-high { border-right: 4px solid #fd7e14; }
.priority-urgent { border-right: 4px solid #dc3545; }
