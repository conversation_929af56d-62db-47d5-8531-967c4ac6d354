{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الجلسات - نظام إدارة دعاوى المحاكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-alt"></i>
        إدارة الجلسات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'sessions:add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة جلسة جديدة
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الجلسة</th>
                        <th>القضية</th>
                        <th>تاريخ الجلسة</th>
                        <th>القاضي</th>
                        <th>قاعة المحكمة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for session in page_obj %}
                    <tr>
                        <td><strong>{{ session.session_number }}</strong></td>
                        <td>
                            <a href="{% url 'cases:detail' session.case.pk %}" class="text-decoration-none">
                                {{ session.case.case_number }}
                            </a>
                            <br>
                            <small class="text-muted">{{ session.case.title|truncatechars:40 }}</small>
                        </td>
                        <td>{{ session.session_date|date:"Y/m/d H:i" }}</td>
                        <td>{{ session.judge_name }}</td>
                        <td>{{ session.courtroom }}</td>
                        <td>
                            <span class="badge 
                                {% if session.status == 'scheduled' %}bg-primary
                                {% elif session.status == 'held' %}bg-success
                                {% elif session.status == 'postponed' %}bg-warning
                                {% else %}bg-danger{% endif %}">
                                {{ session.get_status_display }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'sessions:detail' session.pk %}" class="btn btn-outline-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'sessions:edit' session.pk %}" class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="تصفح الجلسات">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-calendar-times fa-3x mb-3"></i>
            <h4>لا توجد جلسات</h4>
            <p>لم يتم جدولة أي جلسات بعد</p>
            <a href="{% url 'sessions:add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة جلسة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
