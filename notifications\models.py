from django.db import models
from django.contrib.auth.models import User
from cases.models import Case
from sessions.models import Session

class Notification(models.Model):
    """التنبيهات والإشعارات"""
    NOTIFICATION_TYPES = [
        ('session_reminder', 'تذكير بجلسة'),
        ('deadline_reminder', 'تذكير بموعد نهائي'),
        ('case_update', 'تحديث قضية'),
        ('document_added', 'إضافة مستند'),
        ('judgment_issued', 'صدور حكم'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'منخفض'),
        ('medium', 'متوسط'),
        ('high', 'عالي'),
        ('urgent', 'عاجل'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications', verbose_name="المستلم")
    title = models.CharField(max_length=200, verbose_name="العنوان")
    message = models.TextField(verbose_name="الرسالة")
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name="نوع التنبيه")
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium', verbose_name="الأولوية")

    case = models.ForeignKey(Case, on_delete=models.CASCADE, null=True, blank=True, verbose_name="القضية المرتبطة")
    session = models.ForeignKey(Session, on_delete=models.CASCADE, null=True, blank=True, verbose_name="الجلسة المرتبطة")

    is_read = models.BooleanField(default=False, verbose_name="مقروء")
    is_sent = models.BooleanField(default=False, verbose_name="مرسل")

    scheduled_time = models.DateTimeField(null=True, blank=True, verbose_name="وقت الإرسال المجدول")
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name="وقت الإرسال")
    read_at = models.DateTimeField(null=True, blank=True, verbose_name="وقت القراءة")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "تنبيه"
        verbose_name_plural = "التنبيهات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.username}"

class NotificationSettings(models.Model):
    """إعدادات التنبيهات للمستخدمين"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_settings', verbose_name="المستخدم")

    email_notifications = models.BooleanField(default=True, verbose_name="تنبيهات البريد الإلكتروني")
    sms_notifications = models.BooleanField(default=False, verbose_name="تنبيهات الرسائل النصية")
    browser_notifications = models.BooleanField(default=True, verbose_name="تنبيهات المتصفح")

    session_reminders = models.BooleanField(default=True, verbose_name="تذكير الجلسات")
    deadline_reminders = models.BooleanField(default=True, verbose_name="تذكير المواعيد النهائية")
    case_updates = models.BooleanField(default=True, verbose_name="تحديثات القضايا")

    reminder_hours_before = models.PositiveIntegerField(default=24, verbose_name="ساعات التذكير المسبق")

    class Meta:
        verbose_name = "إعدادات التنبيه"
        verbose_name_plural = "إعدادات التنبيهات"

    def __str__(self):
        return f"إعدادات {self.user.username}"
