#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'court_system.settings')
django.setup()

from django.contrib.auth.models import User
from cases.models import Court, CaseType, Party, Case
from court_sessions.models import Session
from documents.models import DocumentType
from notifications.models import NotificationSettings

def create_initial_data():
    print("إنشاء البيانات الأولية...")
    
    # إنشاء المستخدم الإداري
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='مدير',
            last_name='النظام'
        )
        print("✓ تم إنشاء المستخدم الإداري: admin / admin123")
    else:
        admin_user = User.objects.get(username='admin')
        print("✓ المستخدم الإداري موجود بالفعل")
    
    # إنشاء المحاكم
    courts_data = [
        {'name': 'محكمة البداءة المدنية الأولى', 'type': 'مدنية', 'address': 'بغداد - الكرخ'},
        {'name': 'محكمة البداءة الجزائية الأولى', 'type': 'جزائية', 'address': 'بغداد - الرصافة'},
        {'name': 'محكمة الأحوال الشخصية الأولى', 'type': 'أحوال شخصية', 'address': 'بغداد - الكرخ'},
        {'name': 'محكمة التمييز الاتحادية', 'type': 'تمييز', 'address': 'بغداد - المنطقة الخضراء'},
        {'name': 'المحكمة الإدارية العليا', 'type': 'إدارية', 'address': 'بغداد - الجادرية'},
    ]
    
    for court_data in courts_data:
        court, created = Court.objects.get_or_create(
            name=court_data['name'],
            defaults=court_data
        )
        if created:
            print(f"✓ تم إنشاء المحكمة: {court.name}")
    
    # إنشاء أنواع القضايا
    case_types_data = [
        {'name': 'دعوى مدنية', 'description': 'القضايا المدنية العامة'},
        {'name': 'دعوى تجارية', 'description': 'القضايا التجارية والاقتصادية'},
        {'name': 'دعوى جنائية', 'description': 'القضايا الجنائية والجزائية'},
        {'name': 'دعوى أحوال شخصية', 'description': 'قضايا الزواج والطلاق والميراث'},
        {'name': 'دعوى إدارية', 'description': 'القضايا الإدارية والحكومية'},
        {'name': 'دعوى عمالية', 'description': 'قضايا العمل والعمال'},
        {'name': 'دعوى عقارية', 'description': 'قضايا العقارات والأراضي'},
    ]
    
    for case_type_data in case_types_data:
        case_type, created = CaseType.objects.get_or_create(
            name=case_type_data['name'],
            defaults=case_type_data
        )
        if created:
            print(f"✓ تم إنشاء نوع القضية: {case_type.name}")
    
    # إنشاء أنواع المستندات
    document_types_data = [
        {'name': 'مرافعة', 'description': 'المرافعات القانونية'},
        {'name': 'إشعار', 'description': 'الإشعارات الرسمية'},
        {'name': 'حكم', 'description': 'الأحكام القضائية'},
        {'name': 'وكالة', 'description': 'وكالات المحاماة'},
        {'name': 'شهادة', 'description': 'الشهادات والوثائق'},
        {'name': 'تقرير خبرة', 'description': 'تقارير الخبراء'},
        {'name': 'مستند هوية', 'description': 'وثائق الهوية الشخصية'},
    ]
    
    for doc_type_data in document_types_data:
        doc_type, created = DocumentType.objects.get_or_create(
            name=doc_type_data['name'],
            defaults=doc_type_data
        )
        if created:
            print(f"✓ تم إنشاء نوع المستند: {doc_type.name}")
    
    # إنشاء بعض الأطراف النموذجية
    parties_data = [
        {'name': 'أحمد محمد علي', 'party_type': 'plaintiff', 'national_id': '12345678901', 'phone': '07901234567'},
        {'name': 'فاطمة حسن محمود', 'party_type': 'defendant', 'national_id': '12345678902', 'phone': '07901234568'},
        {'name': 'المحامي سعد الدين يوسف', 'party_type': 'lawyer', 'national_id': '12345678903', 'phone': '07901234569'},
        {'name': 'شركة النور للتجارة العامة', 'party_type': 'plaintiff', 'national_id': '12345678904', 'phone': '07901234570'},
    ]
    
    for party_data in parties_data:
        party, created = Party.objects.get_or_create(
            national_id=party_data['national_id'],
            defaults=party_data
        )
        if created:
            print(f"✓ تم إنشاء الطرف: {party.name}")
    
    # إنشاء قضية نموذجية
    if not Case.objects.exists():
        court = Court.objects.first()
        case_type = CaseType.objects.first()
        
        if court and case_type:
            case = Case.objects.create(
                case_number='2024/001',
                case_type=case_type,
                court=court,
                circuit='الدائرة الأولى',
                title='دعوى مطالبة بدين تجاري',
                description='دعوى مطالبة بمبلغ 50,000,000 دينار عراقي كدين تجاري مستحق الأداء',
                status='new',
                created_by=admin_user
            )
            
            # ربط الأطراف بالقضية
            parties = Party.objects.all()[:2]
            case.parties.set(parties)
            
            print(f"✓ تم إنشاء القضية النموذجية: {case.case_number}")
    
    # إنشاء إعدادات التنبيهات للمستخدم الإداري
    settings, created = NotificationSettings.objects.get_or_create(
        user=admin_user,
        defaults={
            'email_notifications': True,
            'browser_notifications': True,
            'session_reminders': True,
            'deadline_reminders': True,
            'case_updates': True,
        }
    )
    if created:
        print("✓ تم إنشاء إعدادات التنبيهات")
    
    print("\n🎉 تم إنشاء جميع البيانات الأولية بنجاح!")
    print("\nبيانات تسجيل الدخول:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")

if __name__ == '__main__':
    create_initial_data()
