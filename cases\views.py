from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Case, CaseType, Court, Party, Judgment
from .forms import CaseForm

@login_required
def case_list(request):
    """قائمة القضايا"""
    cases = Case.objects.all().order_by('-created_at')

    # البحث والتصفية
    search_query = request.GET.get('search')
    status_filter = request.GET.get('status')
    court_filter = request.GET.get('court')
    case_type_filter = request.GET.get('case_type')

    if search_query:
        cases = cases.filter(
            Q(case_number__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if status_filter:
        cases = cases.filter(status=status_filter)

    if court_filter:
        cases = cases.filter(court_id=court_filter)

    if case_type_filter:
        cases = cases.filter(case_type_id=case_type_filter)

    # التصفح
    paginator = Paginator(cases, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'courts': Court.objects.all(),
        'case_types': CaseType.objects.all(),
        'search_query': search_query,
        'status_filter': status_filter,
        'court_filter': court_filter,
        'case_type_filter': case_type_filter,
    }

    return render(request, 'cases/case_list.html', context)

@login_required
def case_detail(request, pk):
    """تفاصيل القضية"""
    case = get_object_or_404(Case, pk=pk)
    judgments = case.judgments.all().order_by('-judgment_date')
    sessions = case.sessions.all().order_by('-session_date')
    documents = case.documents.all().order_by('-uploaded_at')

    context = {
        'case': case,
        'judgments': judgments,
        'sessions': sessions,
        'documents': documents,
    }

    return render(request, 'cases/case_detail.html', context)

@login_required
def case_add(request):
    """إضافة قضية جديدة"""
    if request.method == 'POST':
        form = CaseForm(request.POST)
        if form.is_valid():
            case = form.save(commit=False)
            case.created_by = request.user
            case.save()
            form.save_m2m()
            messages.success(request, 'تم إضافة القضية بنجاح')
            return redirect('cases:detail', pk=case.pk)
    else:
        form = CaseForm()

    return render(request, 'cases/case_form.html', {'form': form, 'title': 'إضافة قضية جديدة'})

@login_required
def case_edit(request, pk):
    """تعديل القضية"""
    case = get_object_or_404(Case, pk=pk)

    if request.method == 'POST':
        form = CaseForm(request.POST, instance=case)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث القضية بنجاح')
            return redirect('cases:detail', pk=case.pk)
    else:
        form = CaseForm(instance=case)

    return render(request, 'cases/case_form.html', {'form': form, 'title': 'تعديل القضية', 'case': case})

@login_required
def case_delete(request, pk):
    """حذف القضية"""
    case = get_object_or_404(Case, pk=pk)

    if request.method == 'POST':
        case.delete()
        messages.success(request, 'تم حذف القضية بنجاح')
        return redirect('cases:list')

    return render(request, 'cases/case_confirm_delete.html', {'case': case})
