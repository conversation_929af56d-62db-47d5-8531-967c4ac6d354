from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class Court(models.Model):
    """نموذج المحاكم"""
    name = models.CharField(max_length=200, verbose_name="اسم المحكمة")
    type = models.CharField(max_length=100, verbose_name="نوع المحكمة")
    address = models.TextField(verbose_name="العنوان")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")

    class Meta:
        verbose_name = "محكمة"
        verbose_name_plural = "المحاكم"

    def __str__(self):
        return self.name

class CaseType(models.Model):
    """أنواع القضايا"""
    name = models.CharField(max_length=100, verbose_name="نوع القضية")
    description = models.TextField(blank=True, verbose_name="الوصف")

    class Meta:
        verbose_name = "نوع قضية"
        verbose_name_plural = "أنواع القضايا"

    def __str__(self):
        return self.name

class Party(models.Model):
    """الخصوم في القضية"""
    PARTY_TYPES = [
        ('plaintiff', 'مدعي'),
        ('defendant', 'مدعى عليه'),
        ('lawyer', 'محامي'),
        ('legal_agent', 'وكيل قانوني'),
    ]

    name = models.CharField(max_length=200, verbose_name="الاسم")
    party_type = models.CharField(max_length=20, choices=PARTY_TYPES, verbose_name="نوع الطرف")
    national_id = models.CharField(max_length=20, blank=True, verbose_name="رقم الهوية")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")
    address = models.TextField(blank=True, verbose_name="العنوان")

    class Meta:
        verbose_name = "طرف"
        verbose_name_plural = "الأطراف"

    def __str__(self):
        return f"{self.name} - {self.get_party_type_display()}"

class Case(models.Model):
    """نموذج القضايا"""
    STATUS_CHOICES = [
        ('new', 'جديدة'),
        ('ongoing', 'جارية'),
        ('postponed', 'مؤجلة'),
        ('closed', 'مغلقة'),
        ('appealed', 'مستأنفة'),
    ]

    case_number = models.CharField(max_length=50, unique=True, verbose_name="رقم القضية")
    registration_date = models.DateField(default=timezone.now, verbose_name="تاريخ القيد")
    case_type = models.ForeignKey(CaseType, on_delete=models.CASCADE, verbose_name="نوع القضية")
    court = models.ForeignKey(Court, on_delete=models.CASCADE, verbose_name="المحكمة")
    circuit = models.CharField(max_length=100, verbose_name="الدائرة")

    title = models.CharField(max_length=300, verbose_name="عنوان القضية")
    description = models.TextField(verbose_name="وصف القضية")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new', verbose_name="حالة القضية")

    parties = models.ManyToManyField(Party, verbose_name="الأطراف")
    assigned_lawyer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المحامي المكلف")

    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_cases', verbose_name="أنشأ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "قضية"
        verbose_name_plural = "القضايا"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.case_number} - {self.title}"

class Judgment(models.Model):
    """الأحكام والقرارات"""
    JUDGMENT_TYPES = [
        ('preliminary', 'أولي'),
        ('final', 'نهائي'),
        ('appeal', 'استئناف'),
        ('cassation', 'تمييز'),
    ]

    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='judgments', verbose_name="القضية")
    judgment_number = models.CharField(max_length=50, verbose_name="رقم الحكم")
    judgment_date = models.DateField(verbose_name="تاريخ الحكم")
    judgment_type = models.CharField(max_length=20, choices=JUDGMENT_TYPES, verbose_name="نوع الحكم")

    content = models.TextField(verbose_name="نص الحكم")
    judge_name = models.CharField(max_length=200, verbose_name="اسم القاضي")

    is_final = models.BooleanField(default=False, verbose_name="حكم نهائي")
    appeal_deadline = models.DateField(null=True, blank=True, verbose_name="موعد انتهاء الاستئناف")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "حكم"
        verbose_name_plural = "الأحكام"
        ordering = ['-judgment_date']

    def __str__(self):
        return f"حكم رقم {self.judgment_number} - {self.case.case_number}"
