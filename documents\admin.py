from django.contrib import admin
from .models import DocumentType, Document, DocumentAccess

@admin.register(DocumentType)
class DocumentTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']

@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ['title', 'case', 'document_type', 'is_confidential', 'uploaded_by', 'uploaded_at']
    list_filter = ['document_type', 'is_confidential', 'uploaded_at']
    search_fields = ['title', 'case__case_number', 'description']

@admin.register(DocumentAccess)
class DocumentAccessAdmin(admin.ModelAdmin):
    list_display = ['document', 'user', 'can_view', 'can_download', 'can_edit', 'granted_by']
    list_filter = ['can_view', 'can_download', 'can_edit']
    search_fields = ['document__title', 'user__username']
