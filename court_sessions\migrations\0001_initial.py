# Generated by Django 4.2.7 on 2025-06-06 10:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("cases", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Session",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "session_number",
                    models.PositiveIntegerField(verbose_name="رقم الجلسة"),
                ),
                (
                    "session_date",
                    models.DateTimeField(verbose_name="تاريخ ووقت الجلسة"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "مجدولة"),
                            ("held", "تمت"),
                            ("postponed", "مؤجلة"),
                            ("cancelled", "ملغية"),
                        ],
                        default="scheduled",
                        max_length=20,
                        verbose_name="حالة الجلسة",
                    ),
                ),
                (
                    "judge_name",
                    models.CharField(max_length=200, verbose_name="اسم القاضي"),
                ),
                (
                    "courtroom",
                    models.CharField(max_length=100, verbose_name="قاعة المحكمة"),
                ),
                ("agenda", models.TextField(verbose_name="جدول أعمال الجلسة")),
                ("minutes", models.TextField(blank=True, verbose_name="محضر الجلسة")),
                (
                    "postponement_reason",
                    models.TextField(blank=True, verbose_name="سبب التأجيل"),
                ),
                (
                    "next_session_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="موعد الجلسة القادمة"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "attendees",
                    models.ManyToManyField(
                        blank=True, to="cases.party", verbose_name="الحاضرون"
                    ),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="cases.case",
                        verbose_name="القضية",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "جلسة",
                "verbose_name_plural": "الجلسات",
                "ordering": ["-session_date"],
                "unique_together": {("case", "session_number")},
            },
        ),
        migrations.CreateModel(
            name="SessionDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان المستند"),
                ),
                (
                    "document_type",
                    models.CharField(max_length=100, verbose_name="نوع المستند"),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to="session_documents/", verbose_name="الملف"
                    ),
                ),
                (
                    "uploaded_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع"),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="court_sessions.session",
                        verbose_name="الجلسة",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="رفع بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "مستند جلسة",
                "verbose_name_plural": "مستندات الجلسات",
            },
        ),
    ]
