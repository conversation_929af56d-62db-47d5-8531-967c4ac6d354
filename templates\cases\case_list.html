{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة القضايا - نظام إدارة دعاوى المحاكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-gavel"></i>
        إدارة القضايا
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'cases:add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة قضية جديدة
            </a>
        </div>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="رقم القضية أو العنوان">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="new" {% if status_filter == 'new' %}selected{% endif %}>جديدة</option>
                    <option value="ongoing" {% if status_filter == 'ongoing' %}selected{% endif %}>جارية</option>
                    <option value="postponed" {% if status_filter == 'postponed' %}selected{% endif %}>مؤجلة</option>
                    <option value="closed" {% if status_filter == 'closed' %}selected{% endif %}>مغلقة</option>
                    <option value="appealed" {% if status_filter == 'appealed' %}selected{% endif %}>مستأنفة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="court" class="form-label">المحكمة</label>
                <select class="form-select" id="court" name="court">
                    <option value="">جميع المحاكم</option>
                    {% for court in courts %}
                    <option value="{{ court.id }}" {% if court_filter == court.id|stringformat:"s" %}selected{% endif %}>
                        {{ court.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="case_type" class="form-label">نوع القضية</label>
                <select class="form-select" id="case_type" name="case_type">
                    <option value="">جميع الأنواع</option>
                    {% for case_type in case_types %}
                    <option value="{{ case_type.id }}" {% if case_type_filter == case_type.id|stringformat:"s" %}selected{% endif %}>
                        {{ case_type.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول القضايا -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم القضية</th>
                        <th>العنوان</th>
                        <th>نوع القضية</th>
                        <th>المحكمة</th>
                        <th>الحالة</th>
                        <th>تاريخ القيد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in page_obj %}
                    <tr>
                        <td>
                            <strong>{{ case.case_number }}</strong>
                        </td>
                        <td>
                            <a href="{% url 'cases:detail' case.pk %}" class="text-decoration-none">
                                {{ case.title|truncatechars:50 }}
                            </a>
                        </td>
                        <td>{{ case.case_type.name }}</td>
                        <td>{{ case.court.name }}</td>
                        <td>
                            <span class="badge status-{{ case.status }}">
                                {{ case.get_status_display }}
                            </span>
                        </td>
                        <td>{{ case.registration_date|date:"Y/m/d" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'cases:detail' case.pk %}" class="btn btn-outline-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'cases:edit' case.pk %}" class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'cases:delete' case.pk %}" class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التصفح -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="تصفح القضايا">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if court_filter %}&court={{ court_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if court_filter %}&court={{ court_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if court_filter %}&court={{ court_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if court_filter %}&court={{ court_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-5">
            <i class="fas fa-gavel fa-3x mb-3"></i>
            <h4>لا توجد قضايا</h4>
            <p>لم يتم العثور على أي قضايا تطابق معايير البحث</p>
            <a href="{% url 'cases:add' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة قضية جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
