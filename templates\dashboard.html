{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام إدارة دعاوى المحاكم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download"></i>
                تصدير التقرير
            </button>
        </div>
    </div>
</div>

<!-- بطاقات الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي القضايا</div>
                        <div class="h5 mb-0 font-weight-bold">{{ total_cases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-gavel fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">القضايا الجديدة</div>
                        <div class="h5 mb-0 font-weight-bold">{{ new_cases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-plus-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">القضايا الجارية</div>
                        <div class="h5 mb-0 font-weight-bold">{{ ongoing_cases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2" style="background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); color: white;">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">القضايا المغلقة</div>
                        <div class="h5 mb-0 font-weight-bold">{{ closed_cases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الجلسات القادمة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calendar-alt"></i>
                الجلسات القادمة
            </div>
            <div class="card-body">
                {% if upcoming_sessions %}
                    <div class="list-group list-group-flush">
                        {% for session in upcoming_sessions %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ session.case.title }}</h6>
                                <p class="mb-1">رقم القضية: {{ session.case.case_number }}</p>
                                <small>{{ session.session_date|date:"Y/m/d H:i" }}</small>
                            </div>
                            <span class="badge bg-primary rounded-pill">جلسة {{ session.session_number }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'sessions:list' %}" class="btn btn-primary">عرض جميع الجلسات</a>
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                        <p>لا توجد جلسات مجدولة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- القضايا الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-gavel"></i>
                القضايا الحديثة
            </div>
            <div class="card-body">
                {% if recent_cases %}
                    <div class="list-group list-group-flush">
                        {% for case in recent_cases %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ case.title }}</h6>
                                <p class="mb-1">رقم القضية: {{ case.case_number }}</p>
                                <small>{{ case.created_at|date:"Y/m/d" }}</small>
                            </div>
                            <span class="badge status-{{ case.status }}">{{ case.get_status_display }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'cases:list' %}" class="btn btn-primary">عرض جميع القضايا</a>
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-gavel fa-3x mb-3"></i>
                        <p>لا توجد قضايا مسجلة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i>
                إحصائيات أنواع القضايا
            </div>
            <div class="card-body">
                {% if case_types_stats %}
                    {% for case_type in case_types_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ case_type.name }}</span>
                        <span class="badge bg-secondary">{{ case_type.case_count }}</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات متاحة</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-building"></i>
                إحصائيات المحاكم
            </div>
            <div class="card-body">
                {% if court_stats %}
                    {% for court in court_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ court.name }}</span>
                        <span class="badge bg-info">{{ court.case_count }}</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات متاحة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if unread_notifications > 0 %}
<!-- تنبيه التنبيهات غير المقروءة -->
<div class="alert alert-info" role="alert">
    <i class="fas fa-bell"></i>
    لديك {{ unread_notifications }} تنبيه غير مقروء.
    <a href="{% url 'notifications:list' %}" class="alert-link">عرض التنبيهات</a>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
// تحديث الوقت كل دقيقة
setInterval(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}
