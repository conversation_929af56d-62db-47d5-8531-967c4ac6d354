from django.db import models
from django.contrib.auth.models import User
from cases.models import Case

class DocumentType(models.Model):
    """أنواع المستندات"""
    name = models.CharField(max_length=100, verbose_name="نوع المستند")
    description = models.TextField(blank=True, verbose_name="الوصف")

    class Meta:
        verbose_name = "نوع مستند"
        verbose_name_plural = "أنواع المستندات"

    def __str__(self):
        return self.name

class Document(models.Model):
    """المستندات والمرفقات"""
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='documents', verbose_name="القضية")
    title = models.CharField(max_length=200, verbose_name="عنوان المستند")
    document_type = models.ForeignKey(DocumentType, on_delete=models.CASCADE, verbose_name="نوع المستند")

    file = models.FileField(upload_to='case_documents/', verbose_name="الملف")
    description = models.TextField(blank=True, verbose_name="الوصف")

    is_confidential = models.BooleanField(default=False, verbose_name="سري")

    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="رفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند"
        verbose_name_plural = "المستندات"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.title} - {self.case.case_number}"

class DocumentAccess(models.Model):
    """صلاحيات الوصول للمستندات"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='access_permissions', verbose_name="المستند")
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    can_view = models.BooleanField(default=True, verbose_name="يمكن المشاهدة")
    can_download = models.BooleanField(default=False, verbose_name="يمكن التحميل")
    can_edit = models.BooleanField(default=False, verbose_name="يمكن التعديل")

    granted_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='granted_permissions', verbose_name="منح بواسطة")
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ المنح")

    class Meta:
        verbose_name = "صلاحية وصول"
        verbose_name_plural = "صلاحيات الوصول"
        unique_together = ['document', 'user']

    def __str__(self):
        return f"{self.user.username} - {self.document.title}"
