from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta

from cases.models import Case, CaseType, Court
from court_sessions.models import Session
from documents.models import Document
from notifications.models import Notification

def login_view(request):
    """صفحة تسجيل الدخول"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            messages.success(request, f'مرحباً بك {user.get_full_name() or user.username}')
            return redirect('dashboard')
        else:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return render(request, 'registration/login.html')

def logout_view(request):
    """تسجيل الخروج"""
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('login')

@login_required
def dashboard(request):
    """لوحة التحكم الرئيسية"""
    # إحصائيات القضايا
    total_cases = Case.objects.count()
    new_cases = Case.objects.filter(status='new').count()
    ongoing_cases = Case.objects.filter(status='ongoing').count()
    closed_cases = Case.objects.filter(status='closed').count()
    
    # الجلسات القادمة
    upcoming_sessions = Session.objects.filter(
        session_date__gte=timezone.now(),
        status='scheduled'
    ).order_by('session_date')[:5]
    
    # التنبيهات غير المقروءة
    unread_notifications = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).count()
    
    # القضايا الحديثة
    recent_cases = Case.objects.order_by('-created_at')[:5]
    
    # إحصائيات حسب نوع القضية
    case_types_stats = CaseType.objects.annotate(
        case_count=Count('case')
    ).order_by('-case_count')[:5]
    
    # إحصائيات حسب المحكمة
    court_stats = Court.objects.annotate(
        case_count=Count('case')
    ).order_by('-case_count')[:5]
    
    context = {
        'total_cases': total_cases,
        'new_cases': new_cases,
        'ongoing_cases': ongoing_cases,
        'closed_cases': closed_cases,
        'upcoming_sessions': upcoming_sessions,
        'unread_notifications': unread_notifications,
        'recent_cases': recent_cases,
        'case_types_stats': case_types_stats,
        'court_stats': court_stats,
    }
    
    return render(request, 'dashboard.html', context)
