# Generated by Django 4.2.7 on 2025-06-06 10:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Case",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "case_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم القضية"
                    ),
                ),
                (
                    "registration_date",
                    models.DateField(
                        default=django.utils.timezone.now, verbose_name="تاريخ القيد"
                    ),
                ),
                ("circuit", models.CharField(max_length=100, verbose_name="الدائرة")),
                (
                    "title",
                    models.CharField(max_length=300, verbose_name="عنوان القضية"),
                ),
                ("description", models.TextField(verbose_name="وصف القضية")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("new", "جديدة"),
                            ("ongoing", "جارية"),
                            ("postponed", "مؤجلة"),
                            ("closed", "مغلقة"),
                            ("appealed", "مستأنفة"),
                        ],
                        default="new",
                        max_length=20,
                        verbose_name="حالة القضية",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "assigned_lawyer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المحامي المكلف",
                    ),
                ),
            ],
            options={
                "verbose_name": "قضية",
                "verbose_name_plural": "القضايا",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CaseType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="نوع القضية")),
                ("description", models.TextField(blank=True, verbose_name="الوصف")),
            ],
            options={
                "verbose_name": "نوع قضية",
                "verbose_name_plural": "أنواع القضايا",
            },
        ),
        migrations.CreateModel(
            name="Court",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم المحكمة")),
                ("type", models.CharField(max_length=100, verbose_name="نوع المحكمة")),
                ("address", models.TextField(verbose_name="العنوان")),
                (
                    "phone",
                    models.CharField(blank=True, max_length=20, verbose_name="الهاتف"),
                ),
            ],
            options={
                "verbose_name": "محكمة",
                "verbose_name_plural": "المحاكم",
            },
        ),
        migrations.CreateModel(
            name="Party",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="الاسم")),
                (
                    "party_type",
                    models.CharField(
                        choices=[
                            ("plaintiff", "مدعي"),
                            ("defendant", "مدعى عليه"),
                            ("lawyer", "محامي"),
                            ("legal_agent", "وكيل قانوني"),
                        ],
                        max_length=20,
                        verbose_name="نوع الطرف",
                    ),
                ),
                (
                    "national_id",
                    models.CharField(
                        blank=True, max_length=20, verbose_name="رقم الهوية"
                    ),
                ),
                (
                    "phone",
                    models.CharField(blank=True, max_length=20, verbose_name="الهاتف"),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="البريد الإلكتروني"
                    ),
                ),
                ("address", models.TextField(blank=True, verbose_name="العنوان")),
            ],
            options={
                "verbose_name": "طرف",
                "verbose_name_plural": "الأطراف",
            },
        ),
        migrations.CreateModel(
            name="Judgment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "judgment_number",
                    models.CharField(max_length=50, verbose_name="رقم الحكم"),
                ),
                ("judgment_date", models.DateField(verbose_name="تاريخ الحكم")),
                (
                    "judgment_type",
                    models.CharField(
                        choices=[
                            ("preliminary", "أولي"),
                            ("final", "نهائي"),
                            ("appeal", "استئناف"),
                            ("cassation", "تمييز"),
                        ],
                        max_length=20,
                        verbose_name="نوع الحكم",
                    ),
                ),
                ("content", models.TextField(verbose_name="نص الحكم")),
                (
                    "judge_name",
                    models.CharField(max_length=200, verbose_name="اسم القاضي"),
                ),
                (
                    "is_final",
                    models.BooleanField(default=False, verbose_name="حكم نهائي"),
                ),
                (
                    "appeal_deadline",
                    models.DateField(
                        blank=True, null=True, verbose_name="موعد انتهاء الاستئناف"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="judgments",
                        to="cases.case",
                        verbose_name="القضية",
                    ),
                ),
            ],
            options={
                "verbose_name": "حكم",
                "verbose_name_plural": "الأحكام",
                "ordering": ["-judgment_date"],
            },
        ),
        migrations.AddField(
            model_name="case",
            name="case_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="cases.casetype",
                verbose_name="نوع القضية",
            ),
        ),
        migrations.AddField(
            model_name="case",
            name="court",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="cases.court",
                verbose_name="المحكمة",
            ),
        ),
        migrations.AddField(
            model_name="case",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_cases",
                to=settings.AUTH_USER_MODEL,
                verbose_name="أنشأ بواسطة",
            ),
        ),
        migrations.AddField(
            model_name="case",
            name="parties",
            field=models.ManyToManyField(to="cases.party", verbose_name="الأطراف"),
        ),
    ]
