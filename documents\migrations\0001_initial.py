# Generated by Django 4.2.7 on 2025-06-06 10:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("cases", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="نوع المستند")),
                ("description", models.TextField(blank=True, verbose_name="الوصف")),
            ],
            options={
                "verbose_name": "نوع مستند",
                "verbose_name_plural": "أنواع المستندات",
            },
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان المستند"),
                ),
                (
                    "file",
                    models.FileField(upload_to="case_documents/", verbose_name="الملف"),
                ),
                ("description", models.TextField(blank=True, verbose_name="الوصف")),
                (
                    "is_confidential",
                    models.BooleanField(default=False, verbose_name="سري"),
                ),
                (
                    "uploaded_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع"),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="cases.case",
                        verbose_name="القضية",
                    ),
                ),
                (
                    "document_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="documents.documenttype",
                        verbose_name="نوع المستند",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="رفع بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "مستند",
                "verbose_name_plural": "المستندات",
                "ordering": ["-uploaded_at"],
            },
        ),
        migrations.CreateModel(
            name="DocumentAccess",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "can_view",
                    models.BooleanField(default=True, verbose_name="يمكن المشاهدة"),
                ),
                (
                    "can_download",
                    models.BooleanField(default=False, verbose_name="يمكن التحميل"),
                ),
                (
                    "can_edit",
                    models.BooleanField(default=False, verbose_name="يمكن التعديل"),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="تاريخ المنح"),
                ),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="access_permissions",
                        to="documents.document",
                        verbose_name="المستند",
                    ),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="granted_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منح بواسطة",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "صلاحية وصول",
                "verbose_name_plural": "صلاحيات الوصول",
                "unique_together": {("document", "user")},
            },
        ),
    ]
